import {Flex, Segmented, TabsProps} from 'antd';
import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import EllipsisButton from '@/components/MCP/EllipsisButton';
import {MCPEditTab} from '@/types/mcp/mcp';
import PublishButton from '../PublishButton';
import MoveButton from '../MoveButton';
import DeleteButton from '../DeleteButton';
import RepealButton from '../RepealButton';
import ActionButtons from '../ActionButtons';
import BaseInfo from './BaseInfo';

const Wrapper = styled.div`
    position: relative;
    margin-top: 16px;
`;

const StyledSegmented = styled(Segmented)`
.custom-segmented.ant-segmented {
    .ant-segmented-thumb {
        border: 1px solid var(--Tokens-primary-color, #0080FF) !important;
        border-radius: 8px !important;
        box-sizing: border-box;
    }
}
`;

const items = [
    {
        key: MCPEditTab.ServerInfo,
        label: '基本信息',
    },
    {
        key: MCPEditTab.Tools,
        label: '工具配置',
    },
    {
        key: MCPEditTab.Analysis,
        label: '数据分析',
    },
];

type Props = TabsProps;
export default function Header({activeKey, onChange}: Props) {

    return (
        <Wrapper>
            <BaseInfo style={{maxWidth: 'calc(50% - 160px)'}} />
            <div style={{display: 'flex', justifyContent: 'center'}}>
                <StyledSegmented
                    options={items.map(item => ({
                        label: item.label,
                        value: item.key,
                    }))}
                    value={activeKey}
                    onChange={onChange as (value: unknown) => void}
                    size="large"
                />
            </div>
            <Flex
                align="center"
                gap={8}
                style={{position: 'absolute', right: 24, top: 0, height: '100%'}}
            >
                <RepealButton />
                <ActionButtons activeTab={activeKey as MCPEditTab} />
                <PublishButton />
                <EllipsisButton>
                    <Button
                        type="text"
                        disabled
                        tooltip="即将上线"
                    >
                        历史
                    </Button>
                    <MoveButton />
                    <DeleteButton />
                </EllipsisButton>
            </Flex>
        </Wrapper>

    );
}
